import json
import logging
import os
from functools import lru_cache
import azure.functions as func
import mysql.connector
from mysql.connector import <PERSON>rror, pooling
import sys

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, create_connection_pool

# Create a connection pool for better performance
cnxpool = create_connection_pool("account_info_pool")

# Error codes
ERROR_CODES = {
    1000: "Missing required parameters",
    1001: "Invalid app_id format",
    2000: "Database connection failed",
    2001: "Username not found",
    "00000": "Invalid session for the user and application"
}

@lru_cache(maxsize=128)
def validate_session(session_number, username, app_id):
    """Validate user session with caching for better performance"""
    try:
        # Convert username to email format by appending @gmail.com
        email = f"{username}@gmail.com"
        
        # Get connection from pool for better performance
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            # Fall back to direct connection using shared config
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # Get the activated_accounts_ID for the email
            cursor.execute("SELECT ID FROM activated_accounts WHERE email = %s", (email,))
            account_result = cursor.fetchone()
            
            if not account_result:
                return False, 2001, f"Username not found: {email}"
            
            account_id = account_result['ID']
            
            # Validate session number
            query = """
            SELECT 1 FROM session_numners 
            WHERE session_number = %s 
            AND activated_accounts_ID = %s 
            AND app_ID = %s
            LIMIT 1
            """
            cursor.execute(query, (session_number, account_id, app_id))
            
            session = cursor.fetchone()
            
            if session:
                return True, None, None
            else:
                return False, "00000", "Invalid session for the user and application"
            
    except Error as e:
        return False, 2000, f"Database error: {str(e)}"
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Account Information function processing a request.')
    
    try:
        # Get parameters from form data or query string
        session_number = req.form.get('session_number') or req.params.get('session_number')
        username = req.form.get('username') or req.params.get('username')
        app_id = req.form.get('app_id') or req.params.get('app_id')
        
        # If parameters not found in form or query, try to get from JSON body
        if not all([session_number, username, app_id]):
            try:
                req_body = req.get_json()
                session_number = session_number or req_body.get('session_number')
                username = username or req_body.get('username')
                app_id = app_id or req_body.get('app_id')
            except ValueError:
                pass
        
        # Validate required parameters
        if not all([session_number, username, app_id]):
            return func.HttpResponse(
                json.dumps({"error": "Missing required parameters: session_number, username, app_id", "error_code": 1000}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Convert app_id to int if it's a string
        try:
            app_id = int(app_id)
        except (ValueError, TypeError):
            return func.HttpResponse(
                json.dumps({"error": "app_id must be a valid integer", "error_code": 1001}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Validate session - now using cached version
        valid_session, error_code, error_message = validate_session(session_number, username, app_id)
        if not valid_session:
            return func.HttpResponse(
                json.dumps({"error": f"Session validation failed: {error_message}", "error_code": error_code}),
                status_code=401,
                mimetype="application/json"
            )
        
        # Get account database information
        try:
            if cnxpool:
                conn = cnxpool.get_connection()
            else:
                # Fall back to direct connection using shared config
                db_config = get_db_config()
                conn = mysql.connector.connect(**db_config)
            
            if conn.is_connected():
                cursor = conn.cursor(dictionary=True)
                
                # Convert username to email format
                email = f"{username}@gmail.com"
                
                # Get the activated_accounts_ID for the email
                cursor.execute("SELECT ID FROM activated_accounts WHERE email = %s", (email,))
                account_result = cursor.fetchone()
                
                if not account_result:
                    return func.HttpResponse(
                        json.dumps({"error": f"Username not found: {email}", "error_code": 2001}),
                        status_code=404,
                        mimetype="application/json"
                    )
                
                account_id = account_result['ID']
                
                # Query the databases table to get all matching records
                query = """
                SELECT * FROM `databases` 
                WHERE id_activated_accounts = %s AND id_app = %s
                """
                cursor.execute(query, (account_id, app_id))
                
                database_results = cursor.fetchall()
                
                # Format results into a structured JSON response
                formatted_results = []
                for row in database_results:
                    # Parse the knowledge_base_index JSON string if it exists
                    knowledge_base_index = None
                    if row.get('knowledge_base_index'):
                        try:
                            knowledge_base_index = json.loads(row['knowledge_base_index'])
                        except json.JSONDecodeError:
                            knowledge_base_index = row['knowledge_base_index']
                    
                    # Format the timestamp
                    time_str = row['time'].isoformat() if row.get('time') else None
                    
                    formatted_results.append({
                        "id_folder_Database": row.get('id_folder_Database'),
                        "id_activated_accounts": row.get('id_activated_accounts'),
                        "id_app": row.get('id_app'),
                        "operation_number": row.get('operation_number'),
                        "condition_databases": row.get('condition_databases'),
                        "type_database": row.get('type_database'),
                        "name_database": row.get('name_database'),
                        "descriptionc_database": row.get('descriptionc_database'),
                        "knowledge_base_index": knowledge_base_index,
                        "time": time_str
                    })
                
                return func.HttpResponse(
                    json.dumps({
                        "success": True,
                        "account_id": account_id,
                        "database_count": len(formatted_results),
                        "databases": formatted_results
                    }),
                    status_code=200,
                    mimetype="application/json"
                )
            
        except Error as e:
            error_msg = f"Database error: {str(e)}"
            logging.error(error_msg)
            return func.HttpResponse(
                json.dumps({"error": error_msg, "error_code": 2000}),
                status_code=500,
                mimetype="application/json"
            )
        finally:
            if 'cursor' in locals() and cursor:
                cursor.close()
            if 'conn' in locals() and conn and conn.is_connected():
                conn.close()
    
    except Exception as e:
        error_msg = f"Error processing request: {str(e)}"
        logging.error(error_msg)
        return func.HttpResponse(
            json.dumps({
                "error": error_msg,
                "error_code": 5000
            }),
            status_code=500,
            mimetype="application/json"
        ) 