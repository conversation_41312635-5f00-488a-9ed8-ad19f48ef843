import json
import logging
import azure.functions as func
import mysql.connector
from mysql.connector import pooling
import random
import string
import secrets
from functools import lru_cache
import sys
import os

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, create_connection_pool

# Create a connection pool for better performance
cnxpool = create_connection_pool("session_pool")

# Faster session number generator using secrets module
def generate_session_number(length=30):
    return secrets.token_urlsafe(length)[:length]

# Cache program validations to avoid repeated DB lookups
@lru_cache(maxsize=100)
def validate_program(program_id, cursor):
    cursor.execute("SELECT 1 FROM app WHERE ID = %s LIMIT 1", (program_id,))
    return cursor.fetchone() is not None

# Cache allowed sessions info to avoid repeated complex queries
@lru_cache(maxsize=100)
def get_allowed_sessions(program_id, cursor):
    cursor.execute("""
        SELECT allowed_number 
        FROM number_sessions_allowed 
        INNER JOIN app ON number_sessions_allowed.ID = app.number_sessions_allowed_ID 
        WHERE app.ID = %s
        LIMIT 1
    """, (program_id,))
    result = cursor.fetchone()
    return result['allowed_number'] if result else None

# Optimized account validation
def validate_account(email, password, cursor):
    cursor.execute("SELECT ID FROM activated_accounts WHERE email = %s AND password = %s AND Account_status != 0 LIMIT 1", 
                  (email, password))
    account = cursor.fetchone()
    return account['ID'] if account else None

# Get current sessions efficiently using a single query - sorted by time to ensure FIFO order
def get_current_sessions(program_id, account_id, cursor):
    cursor.execute("""
        SELECT session_number, time 
        FROM session_numners 
        WHERE app_ID = %s AND activated_accounts_ID = %s
        ORDER BY time ASC
    """, (program_id, account_id))
    return cursor.fetchall()

# Manage sessions when limit is reached - implement FIFO queue principle
def manage_session_queue(sessions, max_sessions, cursor):
    if len(sessions) >= max_sessions:
        # Delete the oldest session(s) to make room for new one
        oldest_session = sessions[0]['session_number']
        logging.info(f"Session limit reached. Removing oldest session: {oldest_session}")
        cursor.execute("DELETE FROM session_numners WHERE session_number = %s", 
                      (oldest_session,))
        return oldest_session
    return None

# Create a new session
def create_session(program_id, account_id, cursor):
    new_session = generate_session_number()
    cursor.execute("""
        INSERT INTO session_numners (session_number, app_ID, activated_accounts_ID) 
        VALUES (%s, %s, %s)
    """, (new_session, program_id, account_id))
    return new_session

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Session management function processing a request.')

    try:
        # Parse request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 6001}), 
                status_code=200, 
                mimetype="application/json"
            )

        # Extract parameters
        program_id = req_body.get('program_id')
        email = req_body.get('email')
        password = req_body.get('password')

        # Validate parameters
        if not all([program_id, email, password]):
            return func.HttpResponse(
                json.dumps({'error': 5001}), 
                status_code=200, 
                mimetype="application/json"
            )

        # Get connection from pool
        if cnxpool:
            connection = cnxpool.get_connection()
        else:
            # Fall back to a direct connection using shared config
            db_config = get_db_config()
            connection = mysql.connector.connect(**db_config)
            
        cursor = connection.cursor(dictionary=True)
        
        # Use a transaction for all operations to ensure data integrity
        connection.autocommit = False

        try:
            # Step 1: Verify the program ID (using cached function)
            if not validate_program(program_id, cursor):
                return func.HttpResponse(
                    json.dumps({'error': 1}), 
                    status_code=200, 
                    mimetype="application/json"
                )

            # Step 2: Check for the user's email and password
            account_id = validate_account(email, password, cursor)
            if not account_id:
                return func.HttpResponse(
                    json.dumps({'error': 2}), 
                    status_code=200, 
                    mimetype="application/json"
                )

            # Step 3: Get the maximum number of allowed sessions (using cached function)
            max_sessions = get_allowed_sessions(program_id, cursor)
            if not max_sessions:
                return func.HttpResponse(
                    json.dumps({'error': 3}), 
                    status_code=200, 
                    mimetype="application/json"
                )

            # Step 4: Get current sessions (already sorted by time in the query to ensure FIFO order)
            sessions = get_current_sessions(program_id, account_id, cursor)
            current_sessions = len(sessions)
            
            # Step 5: Session queue management - follow FIFO principle
            removed_session = None
            if current_sessions >= max_sessions:
                # Remove oldest session following queue principle
                removed_session = manage_session_queue(sessions, max_sessions, cursor)
                logging.info(f"Removed oldest session {removed_session} to maintain queue principle")
            
            # Step 6: Create new session (added to the end of the queue)
            new_session = create_session(program_id, account_id, cursor)
            logging.info(f"Created new session {new_session} for account {account_id}")
            
            # Commit all changes
            connection.commit()
            
            response_data = {
                'session_number': new_session, 
                'activated_accounts_ID': account_id
            }
            
            # Include information about queue management if applicable
            if removed_session:
                response_data['queue_management'] = {
                    'status': 'oldest_session_removed',
                    'removed_session': removed_session
                }
            
            return func.HttpResponse(
                json.dumps(response_data), 
                status_code=200, 
                mimetype="application/json"
            )

        except Exception as e:
            # Rollback in case of errors
            connection.rollback()
            raise e

    except mysql.connector.Error as e:
        logging.error(f"Database error: {e}")
        return func.HttpResponse(
            json.dumps({'error': 0}), 
            status_code=200, 
            mimetype="application/json"
        )

    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return func.HttpResponse(
            json.dumps({'error': 11001}), 
            status_code=200, 
            mimetype="application/json"
        )

    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'connection' in locals() and connection and connection.is_connected():
            connection.close()