import json
import logging
import os
import uuid
import requests
from functools import lru_cache
import azure.functions as func
import mysql.connector
from mysql.connector import Error, pooling
import sys
from datetime import datetime
from ip2geotools.databases.noncommercial import DbIpCity

# Add custom JSON encoder to handle datetime serialization
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(DateTimeEncoder, self).default(obj)

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, create_connection_pool

# Create a connection pool for better performance
cnxpool = create_connection_pool("notifications_pool")

# Error codes
ERROR_CODES = {
    1000: "Missing required parameters",
    1001: "Invalid app_id format",
    1002: "Invalid service_number format",
    2000: "Database connection failed",
    2001: "Username not found",
    "00000": "Invalid session for the user and application",
    4000: "Failed to update notification status",
    5000: "General server error"
}

def get_country_from_ip(ip_address):
    """Get country name from IP address using ip2geotools"""
    try:
        if not ip_address or ip_address == "::1" or ip_address.startswith("127."):
            return "Local"
        
        # Strip out port number if present (format: IP:PORT)
        if ":" in ip_address:
            ip_address = ip_address.split(":")[0]
            logging.info(f"Stripped port from IP address, using: {ip_address}")
        
        response = DbIpCity.get(ip_address, api_key="free")
        return response.country if response.country else "Unknown"
    except Exception as e:
        logging.warning(f"Failed to get country from IP {ip_address}: {str(e)}")
        return "Unknown"

def update_session_country(session_number, ip_address):
    """Update the session_numners table with the country name based on IP address"""
    if not ip_address:
        logging.warning("No IP address provided for country lookup")
        return
        
    try:
        logging.info(f"Updating country for session {session_number} with IP {ip_address}")
        country_name = get_country_from_ip(ip_address)
        
        if country_name == "Unknown":
            logging.warning(f"Could not determine country for IP {ip_address}")
        else:
            logging.info(f"Detected country: {country_name} for IP {ip_address}")
        
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor()
            
            # Update the session with country name and IP address
            update_query = """
                UPDATE session_numners 
                SET name_country = %s, IP_Address = %s
                WHERE session_number = %s
            """
            
            cursor.execute(update_query, (country_name, ip_address, session_number))
            conn.commit()
            logging.info(f"Updated session {session_number} with country: {country_name}")
            
    except Error as e:
        logging.error(f"Database error in update_session_country: {str(e)}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

@lru_cache(maxsize=128)
def validate_session(session_number, username, app_id, service_number=1):
    """Validate user session with caching for better performance"""
    try:
        # Convert username to email format by appending @gmail.com
        email = f"{username}@gmail.com"
        
        # Get connection from pool for better performance
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            # Fall back to direct connection using shared config
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # Get the activated_accounts_ID for the email
            cursor.execute("SELECT ID FROM activated_accounts WHERE email = %s", (email,))
            account_result = cursor.fetchone()
            
            if not account_result:
                return False, 2001, f"Username not found: {email}", None
            
            account_id = account_result['ID']
            
            # Validate session number
            query = """
            SELECT 1 FROM session_numners 
            WHERE session_number = %s 
            AND activated_accounts_ID = %s 
            AND app_ID = %s
            LIMIT 1
            """
            cursor.execute(query, (session_number, account_id, app_id))
            
            session = cursor.fetchone()
            
            if session:
                return True, None, None, account_id
            else:
                return False, "00000", "Invalid session for the user and application", None
            
    except Error as e:
        return False, 2000, f"Database error: {str(e)}", None
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def get_user_data(account_id, app_id, service_number=1):
    """Get user data including sessions, points, databases but NOT notifications"""
    try:
        logging.info(f"Getting user data for account_id={account_id}, app_id={app_id}, service_number={service_number}")
        
        if cnxpool:
            logging.info("Using connection pool")
            conn = cnxpool.get_connection()
        else:
            logging.info("Using direct connection")
            db_config = get_db_config()
            logging.info(f"DB config: host={db_config['host']}, user={db_config['user']}, database={db_config['database']}")
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            logging.info("Database connection established successfully")
            cursor = conn.cursor(dictionary=True)
            
            # Get active sessions count
            try:
                logging.info("Executing session count query")
                cursor.execute("""
                    SELECT COUNT(*) as session_count 
                    FROM session_numners 
                    WHERE activated_accounts_ID = %s AND app_ID = %s
                """, (account_id, app_id))
                sessions_data = cursor.fetchone()
                session_count = sessions_data['session_count'] if sessions_data else 0
                logging.info(f"Session count query successful: {session_count}")
            except Error as e:
                logging.error(f"Error in session count query: {str(e)}")
                session_count = 0
            
            # Get points for each service type with service name
            # Removed service_number filtering to ensure points are returned correctly
            try:
                logging.info("Executing points query")
                cursor.execute("""
                    SELECT p.types_servies_ID, p.number_points, ts.services_name
                    FROM points p
                    JOIN types_servies ts ON p.types_servies_ID = ts.ID
                    WHERE p.activated_accounts_ID = %s
                """, (account_id,))
                points_data = cursor.fetchall() or []
                logging.info(f"Points query successful: {len(points_data)} records")
            except Error as e:
                logging.error(f"Error in points query: {str(e)}")
                points_data = []
            
            # No longer calculating database count as requested
            logging.info("Skipping database count calculation")
            db_count = 0
            
            # Removed the notifications query to avoid duplication
            
            logging.info("All queries completed successfully")
            return {
                "session_count": session_count,
                "points": points_data,
                "database_count": db_count,
                "service_number": service_number
            }
    except Error as e:
        logging.error(f"Database error in get_user_data: {str(e)}")
        return None
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def get_new_notifications(account_id):
    """Get only new notifications that the user hasn't seen yet"""
    try:
        logging.info(f"Getting new notifications for account_id={account_id}")
        
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # First, check if the device was created on the same date as the activated_accounts entry
            cursor.execute("""
                SELECT DATE(time) as account_creation_date
                FROM activated_accounts
                WHERE ID = %s
            """, (account_id,))
            
            account_result = cursor.fetchone()
            if not account_result:
                logging.warning(f"Account ID {account_id} not found in activated_accounts table")
                return []
            
            account_creation_date = account_result['account_creation_date']
            logging.info(f"Account creation date: {account_creation_date}")
            
            # Get only unread notifications, excluding those created on or before the account creation date
            cursor.execute("""
                SELECT n.ID, n.title, n.content, n.link, n.created_at, 
                       nt.type_name, nt.icon, un.read_at
                FROM user_notifications un
                JOIN notifications n ON un.notification_ID = n.ID
                JOIN notification_types nt ON n.notification_type_ID = nt.ID
                WHERE un.activated_accounts_ID = %s 
                AND un.is_read = 0
                AND DATE(n.created_at) > %s
                ORDER BY n.created_at DESC
            """, (account_id, account_creation_date))
            
            new_notifications = cursor.fetchall() or []
            logging.info(f"Found {len(new_notifications)} unread notifications after filtering")
            
            return new_notifications
    except Error as e:
        logging.error(f"Database error in get_new_notifications: {str(e)}")
        return None
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def update_notification_status(account_id, notification_ids):
    """Mark notifications as read"""
    if not notification_ids:
        return True
        
    try:
        logging.info(f"Updating notification status for account_id={account_id}, notification_ids={notification_ids}")
        
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor()
            
            # Create placeholders for SQL query
            placeholders = ', '.join(['%s'] * len(notification_ids))
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Update all notifications to read
            update_query = f"""
                UPDATE user_notifications 
                SET is_read = 1, read_at = %s
                WHERE activated_accounts_ID = %s 
                AND notification_ID IN ({placeholders})
            """
            
            # Combine parameters
            params = [current_time, account_id] + notification_ids
            
            # Execute update
            cursor.execute(update_query, params)
            conn.commit()
            logging.info(f"Updated {cursor.rowcount} notification records")
            
            return True
    except Error as e:
        logging.error(f"Database error in update_notification_status: {str(e)}")
        return False
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def get_app_wide_notifications(account_id, app_id):
    """Get app-wide notifications that haven't been delivered to this user yet"""
    try:
        logging.info(f"Getting app-wide notifications for account_id={account_id}, app_id={app_id}")
        
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # First, check if the device was created on the same date as the activated_accounts entry
            cursor.execute("""
                SELECT DATE(time) as account_creation_date
                FROM activated_accounts
                WHERE ID = %s
            """, (account_id,))
            
            account_result = cursor.fetchone()
            if not account_result:
                logging.warning(f"Account ID {account_id} not found in activated_accounts table")
                return []
            
            account_creation_date = account_result['account_creation_date']
            logging.info(f"Account creation date for app notifications: {account_creation_date}")
            
            # Get app-wide notifications that haven't been delivered to this user
            # Excluding those created on or before the account creation date
            query = """
                SELECT an.ID as app_notification_id, n.ID as notification_id, 
                       n.title, n.content, n.link, n.created_at, 
                       nt.type_name, nt.icon
                FROM app_notifications an
                JOIN notifications n ON an.notification_ID = n.ID
                JOIN notification_types nt ON n.notification_type_ID = nt.ID
                WHERE an.app_ID = %s
                AND NOT EXISTS (
                    SELECT 1 FROM delivered_app_notifications dan 
                    WHERE dan.app_notifications_ID = an.ID 
                    AND dan.activated_accounts_ID = %s
                )
                AND DATE(n.created_at) > %s
                ORDER BY n.created_at DESC
            """
            
            cursor.execute(query, (app_id, account_id, account_creation_date))
            app_notifications = cursor.fetchall() or []
            logging.info(f"Found {len(app_notifications)} undelivered app-wide notifications after filtering")
            
            return app_notifications
    except Error as e:
        logging.error(f"Database error in get_app_wide_notifications: {str(e)}")
        return None
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def mark_app_notification_delivered(account_id, app_notification_id):
    """Mark an app-wide notification as delivered to a user and increment its delivery count"""
    try:
        logging.info(f"Marking app notification {app_notification_id} as delivered to account {account_id}")
        
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor()
            
            # First, insert a record into delivered_app_notifications
            insert_query = """
                INSERT INTO delivered_app_notifications 
                (activated_accounts_ID, app_notifications_ID) 
                VALUES (%s, %s)
            """
            cursor.execute(insert_query, (account_id, app_notification_id))
            
            # Then, increment the is_read counter in app_notifications
            update_query = """
                UPDATE app_notifications 
                SET is_read = is_read + 1 
                WHERE ID = %s
            """
            cursor.execute(update_query, (app_notification_id,))
            
            # Commit the transaction
            conn.commit()
            logging.info(f"Successfully marked app notification {app_notification_id} as delivered")
            
            return True
    except Error as e:
        logging.error(f"Database error in mark_app_notification_delivered: {str(e)}")
        return False
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Notifications function processing a request.')
    
    try:
        # Parse request body for credentials
        try:
            req_body = req.get_json()
            logging.info("Request body parsed as JSON")
            
            # Check if we have the expected structure with credentials
            credentials = req_body.get('credentials', {})
            
            session_number = credentials.get('session_number')
            username = credentials.get('username')
            app_id = credentials.get('app_id')
            service_number = credentials.get('service_number', 1)  # Default to 1 if not provided
            
            logging.info(f"Extracted from JSON: session_number={session_number}, username={username}, app_id={app_id}, service_number={service_number}")
            
        except ValueError:
            # If not JSON, try form data or query parameters
            logging.info("Request body is not JSON, trying form data or query parameters")
            session_number = req.form.get('session_number') or req.params.get('session_number')
            username = req.form.get('username') or req.params.get('username')
            app_id = req.form.get('app_id') or req.params.get('app_id')
            service_number = req.form.get('service_number') or req.params.get('service_number', '1')  # Default to 1 if not provided
            
            logging.info(f"Extracted from form/params: session_number={session_number}, username={username}, app_id={app_id}, service_number={service_number}")
        
        # Validate required parameters
        if not all([session_number, username, app_id]):
            logging.warning("Missing required parameters")
            return func.HttpResponse(
                json.dumps({"error": "Missing required parameters: session_number, username, app_id", "error_code": 1000}, cls=DateTimeEncoder),
                status_code=400,
                mimetype="application/json"
            )
        
        # Get client IP address from request headers or context
        client_ip = req.headers.get('X-Forwarded-For', '').split(',')[0].strip() or \
                   req.headers.get('X-Real-IP', '') or \
                   req.headers.get('X-Client-IP', '') or \
                   req.headers.get('CF-Connecting-IP', '') or \
                   getattr(req, 'client_ip', '') or \
                   '127.0.0.1'
        
        # Clean IP address - remove port if present
        if client_ip and ":" in client_ip:
            client_ip = client_ip.split(":")[0]
        
        logging.info(f"Client IP (cleaned): {client_ip}")
        
        # Update session with country information
        update_session_country(session_number, client_ip)
        
        # Convert app_id to int if it's a string
        try:
            app_id = int(app_id)
            logging.info(f"Converted app_id to int: {app_id}")
        except (ValueError, TypeError):
            logging.warning(f"Invalid app_id format: {app_id}")
            return func.HttpResponse(
                json.dumps({"error": "app_id must be a valid integer", "error_code": 1001}, cls=DateTimeEncoder),
                status_code=400,
                mimetype="application/json"
            )
            
        # Convert service_number to int if it's a string
        try:
            service_number = int(service_number)
            logging.info(f"Converted service_number to int: {service_number}")
        except (ValueError, TypeError):
            logging.warning(f"Invalid service_number format: {service_number}")
            return func.HttpResponse(
                json.dumps({"error": "service_number must be a valid integer", "error_code": 1002}, cls=DateTimeEncoder),
                status_code=400,
                mimetype="application/json"
            )
        
        # Validate session - now using cached version
        logging.info(f"Validating session: session_number={session_number}, username={username}, app_id={app_id}, service_number={service_number}")
        valid_session, error_code, error_message, account_id = validate_session(session_number, username, app_id, service_number)
        if not valid_session:
            logging.warning(f"Session validation failed: {error_message}")
            return func.HttpResponse(
                json.dumps({"error": f"Session validation failed: {error_message}", "error_code": error_code}, cls=DateTimeEncoder),
                status_code=401,
                mimetype="application/json"
            )
        
        logging.info(f"Session validated successfully. Account ID: {account_id}")
        
        # Get user data
        logging.info(f"Getting user data for account_id={account_id}, app_id={app_id}, service_number={service_number}")
        user_data = get_user_data(account_id, app_id, service_number)
        if not user_data:
            logging.error("Failed to retrieve user data")
            return func.HttpResponse(
                json.dumps({"error": "Failed to retrieve user data", "error_code": 5000}, cls=DateTimeEncoder),
                status_code=500,
                mimetype="application/json"
            )
        
        # Get new user-specific notifications
        logging.info(f"Getting new user-specific notifications for account_id={account_id}")
        new_notifications = get_new_notifications(account_id)
        if new_notifications is None:  # Only return error if it's None, empty list is valid
            logging.error("Failed to retrieve new notifications")
            return func.HttpResponse(
                json.dumps({"error": "Failed to retrieve new notifications", "error_code": 5000}, cls=DateTimeEncoder),
                status_code=500,
                mimetype="application/json"
            )
        
        # Get app-wide notifications that haven't been delivered to this user
        logging.info(f"Getting app-wide notifications for account_id={account_id}, app_id={app_id}")
        app_notifications = get_app_wide_notifications(account_id, app_id)
        if app_notifications is None:  # Only return error if it's None, empty list is valid
            logging.error("Failed to retrieve app-wide notifications")
            return func.HttpResponse(
                json.dumps({"error": "Failed to retrieve app-wide notifications", "error_code": 5000}, cls=DateTimeEncoder),
                status_code=500,
                mimetype="application/json"
            )
        
        # Extract IDs of new user-specific notifications to mark them as read automatically
        notification_ids_to_update = [notification['ID'] for notification in new_notifications] if new_notifications else []
        
        # Automatically mark user-specific notifications as read since they will be sent to the user
        if notification_ids_to_update:
            logging.info(f"Automatically marking notifications as read: {notification_ids_to_update}")
            success = update_notification_status(account_id, notification_ids_to_update)
            if not success:
                logging.warning(f"Failed to automatically update notification status for IDs: {notification_ids_to_update}")
                # Continue with the response even if update fails - we don't want to block the response
        
        # Process app-wide notifications - mark them as delivered
        app_notification_ids = []
        for notification in app_notifications:
            app_notification_id = notification['app_notification_id']
            app_notification_ids.append(app_notification_id)
            success = mark_app_notification_delivered(account_id, app_notification_id)
            if not success:
                logging.warning(f"Failed to mark app notification {app_notification_id} as delivered")
                # Continue with the response even if update fails
        
        # Combine user-specific and app-wide notifications
        all_new_notifications = new_notifications + app_notifications
        
        # Return success response with the data - only including new_notifications, not all notifications
        logging.info("Preparing successful response")
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "status": "request_processed",
                "user_data": user_data,
                "new_notifications": all_new_notifications,
                "service_number": service_number
            }, cls=DateTimeEncoder),
            status_code=200,
            mimetype="application/json"
        )
    
    except Exception as e:
        error_msg = f"Error processing request: {str(e)}"
        logging.error(error_msg)
        return func.HttpResponse(
            json.dumps({
                "error": error_msg,
                "error_code": 5000
            }, cls=DateTimeEncoder),
            status_code=500,
            mimetype="application/json"
        ) 