import json
import logging
import azure.functions as func
import mysql.connector
from mysql.connector import pooling
import uuid
from datetime import datetime, timedelta
import pytz
import re
from functools import lru_cache
import os
import sys

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, create_connection_pool

# Create a connection pool for better performance
cnxpool = create_connection_pool("activate_accounts_pool")

# Cache for email validation pattern
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@gmail\.com$')

# Precompile regular expressions
@lru_cache(maxsize=1)
def get_email_pattern():
    return EMAIL_PATTERN

def generate_confirmation_code():
    return uuid.uuid4().hex

@lru_cache(maxsize=100)
def validate_email(email):
    """Cached email validation for better performance"""
    pattern = get_email_pattern()
    return bool(pattern.match(email))

@lru_cache(maxsize=100)
def validate_password(password):
    """Simple password validation with caching"""
    return len(password) >= 8

def check_existing_email(cursor, email):
    """Check if email already exists in database"""
    cursor.execute("SELECT 1 FROM activated_accounts WHERE email = %s LIMIT 1", (email,))
    return cursor.fetchone() is not None

def activate_account(cursor, email, password, app_ID):
    """Directly activate account"""
    try:
        # Set SQL safe updates off to allow for certain operations
        cursor.execute("SET SQL_SAFE_UPDATES = 0;")
        
        # Start a transaction
        cursor.execute("START TRANSACTION")
        
        # Insert directly into activated_accounts
        cursor.execute(
            "INSERT INTO activated_accounts (email, password) VALUES (%s, %s)", 
            (email, password)
        )
        activated_account_id = cursor.lastrowid
        
        # Fetch all types_servies IDs
        cursor.execute("SELECT ID, free_points FROM types_servies")
        types_servies_list = cursor.fetchall()
        
        # Add free points for each service type
        for service in types_servies_list:
            cursor.execute(
                "INSERT INTO points (number_points, activated_accounts_ID, types_servies_ID) VALUES (%s, %s, %s)",
                (service['free_points'], activated_account_id, service['ID'])
            )
        
        # Commit the transaction
        cursor.execute("COMMIT")
        return True, activated_account_id
    except Exception as e:
        cursor.execute("ROLLBACK")
        logging.error(f"Error during account activation: {str(e)}")
        return False, None

def activate_account_from_verification(cursor, confirmation_code):
    """Activate account from verification code"""
    try:
        # Set SQL safe updates off to allow for certain operations
        cursor.execute("SET SQL_SAFE_UPDATES = 0;")
        
        # Verify the existence and validity of the confirmation code
        cursor.execute(
            "SELECT * FROM account_verification WHERE confirmation_code = %s", 
            (confirmation_code,)
        )
        verification = cursor.fetchone()
        
        if not verification or verification['end time'] < datetime.utcnow():
            return False, "expired"
            
        # Check if this is a password reset (Type=2) or regular activation (Type=1)
        if verification['Type'] == 2:  # Password reset flow
            # Fetch the inactive account details for password reset
            cursor.execute(
                "SELECT * FROM inactive_accounts WHERE ID = %s", 
                (verification['Inactive_accounts_ID'],)
            )
            inactive_account = cursor.fetchone()
            
            if not inactive_account:
                return False, "invalid"
                
            # Return the email and confirmation code for password reset form
            return True, "password_reset", inactive_account['email'], confirmation_code
            
        # Regular account activation (Type=1)
        # Fetch the inactive account
        cursor.execute(
            "SELECT * FROM inactive_accounts WHERE ID = %s", 
            (verification['Inactive_accounts_ID'],)
        )
        inactive_account = cursor.fetchone()
        
        if not inactive_account:
            return False, "invalid"
            
        # Start transaction for activation and deletion
        cursor.execute("START TRANSACTION")
        
        try:
            # Transfer data from inactive_accounts to activated_accounts
            cursor.execute(
                "INSERT INTO activated_accounts (email, password) VALUES (%s, %s)",
                (inactive_account['email'], inactive_account['password'])
            )
            activated_account_id = cursor.lastrowid
            
            # Fetch all types_servies IDs
            cursor.execute("SELECT ID, free_points FROM types_servies")
            types_servies_list = cursor.fetchall()
            
            for service in types_servies_list:
                cursor.execute(
                    "INSERT INTO points (number_points, activated_accounts_ID, types_servies_ID) VALUES (%s, %s, %s)",
                    (service['free_points'], activated_account_id, service['ID'])
                )
                
            # Delete from account_verification and inactive_accounts
            cursor.execute(
                "DELETE av FROM account_verification av "
                "JOIN inactive_accounts ia ON av.Inactive_accounts_ID = ia.ID "
                "WHERE ia.email = %s",
                (inactive_account['email'],)
            )
            cursor.execute(
                "DELETE FROM inactive_accounts WHERE email = %s",
                (inactive_account['email'],)
            )
            
            cursor.execute("COMMIT")
            return True, "success"
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            logging.error(f"Error during account activation from verification: {str(e)}")
            return True, "partial"  # Account activated but cleanup failed
            
    except Exception as e:
        logging.error(f"Error during verification process: {str(e)}")
        return False, "error"

def update_user_password(cursor, email, new_password):
    """Update user's password in the activated_accounts table"""
    try:
        # Set SQL safe updates off
        cursor.execute("SET SQL_SAFE_UPDATES = 0;")
        
        # Update the password for the user with the given email
        cursor.execute(
            "UPDATE activated_accounts SET password = %s WHERE email = %s",
            (new_password, email)
        )
        
        if cursor.rowcount > 0:
            # Clean up the password reset verification data
            cursor.execute(
                "DELETE av FROM account_verification av "
                "JOIN inactive_accounts ia ON av.Inactive_accounts_ID = ia.ID "
                "WHERE ia.email = %s AND av.Type = 2",
                (email,)
            )
            cursor.execute(
                "DELETE FROM inactive_accounts WHERE email = %s AND app_ID = 1",
                (email,)
            )
            return True
        return False
    except Exception as e:
        logging.error(f"Error updating password: {str(e)}")
        return False

def get_password_reset_html(email, confirmation_code):
    """Return HTML for password reset form"""
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Reset Your Password</title>
        <style>
            body {{ font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }}
            .form-box {{ background-color: #fff; border-radius: 10px; padding: 30px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; max-width: 400px; width: 100%; }}
            .form-box h1 {{ color: #4285F4; margin-bottom: 20px; }}
            .form-group {{ margin-bottom: 15px; text-align: left; }}
            .form-group label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
            .form-group input {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
            .error-message {{ color: #ff0000; margin-top: 5px; display: none; text-align: left; }}
            .submit-button {{ background-color: #4285F4; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; font-size: 16px; margin-top: 10px; }}
            .submit-button:hover {{ background-color: #3367d6; }}
            .form-info {{ margin-bottom: 20px; color: #666; }}
        </style>
    </head>
    <body>
        <div class="form-box">
            <h1>Reset Your Password</h1>
            <p class="form-info">Please enter your new password for the account <strong>{email}</strong></p>
            
            <form id="passwordResetForm">
                <div class="form-group">
                    <label for="password">New Password:</label>
                    <input type="password" id="password" name="password" required>
                    <div id="passwordError" class="error-message">Password must be at least 8 characters long.</div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                    <div id="confirmError" class="error-message">Passwords do not match.</div>
                </div>
                
                <input type="hidden" id="email" name="email" value="{email}">
                <input type="hidden" id="confirmationCode" name="confirmationCode" value="{confirmation_code}">
                
                <button type="submit" class="submit-button">Reset Password</button>
            </form>
        </div>

        <script>
            document.getElementById('passwordResetForm').addEventListener('submit', function(e) {{
                e.preventDefault();
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const email = document.getElementById('email').value;
                const confirmationCode = document.getElementById('confirmationCode').value;
                
                // Reset error messages
                document.getElementById('passwordError').style.display = 'none';
                document.getElementById('confirmError').style.display = 'none';
                
                // Validate password length
                if (password.length < 8) {{
                    document.getElementById('passwordError').style.display = 'block';
                    return;
                }}
                
                // Validate password match
                if (password !== confirmPassword) {{
                    document.getElementById('confirmError').style.display = 'block';
                    return;
                }}
                
                // Submit form to reset password endpoint
                fetch('/api/ActivateAccounts/resetpassword', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json'
                    }},
                    body: JSON.stringify({{
                        email: email,
                        password: password,
                        confirmation_code: confirmationCode
                    }})
                }})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        window.location.href = '/api/ActivateAccounts/passwordreset/success';
                    }} else {{
                        alert('Error resetting password: ' + data.message);
                    }}
                }})
                .catch(error => {{
                    alert('An error occurred: ' + error.message);
                }});
            }});
        </script>
    </body>
    </html>
    """
    return html

def get_password_reset_success_html():
    """Return HTML for successful password reset"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Password Reset Successful</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #4CAF50; }
            .message-box p { color: #333; }
            .icon { font-size: 64px; color: #4CAF50; }
            .info-text { display: inline-block; margin-top: 20px; color: #333; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">✅</div>
            <h1>Password Reset Successful!</h1>
            <p>Your password has been updated successfully. You can now log in with your new password.</p>
            <p class="info-text">Please return to the login page to sign in.</p>
        </div>
    </body>
    </html>
    """
    return html

def get_expired_html():
    """Return HTML for expired verification link"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Expired Verification</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #ff0000; }
            .icon { font-size: 64px; color: #ff0000; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">⏱️</div>
            <h1>The verification link has expired.</h1>
        </div>
    </body>
    </html>
    """
    return html

def get_invalid_html():
    """Return HTML for invalid verification link"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Invalid Verification</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #ff0000; }
            .icon { font-size: 64px; color: #ff0000; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">❌</div>
            <h1>Invalid verification link.</h1>
        </div>
    </body>
    </html>
    """
    return html

def get_success_html():
    """Return HTML for successful account activation"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Account Activated</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #4CAF50; }
            .message-box p { color: #333; }
            .icon { font-size: 64px; color: #4CAF50; }
            .thank-you { color: #333; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">✅</div>
            <h1>Account Activated Successfully!</h1>
            <p>Thank you for verifying your email. A free coupon has been added to your account to try our service.</p>
            <p class="thank-you">We appreciate your support!</p>
        </div>
    </body>
    </html>
    """
    return html

def get_partial_success_html():
    """Return HTML for partially successful account activation"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Account Activated</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #4CAF50; }
            .message-box p { color: #333; }
            .icon { font-size: 64px; color: #4CAF50; }
            .thank-you { color: #333; font-weight: bold; }
            .warning { color: #FF9800; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">✅</div>
            <h1>Account Activated Successfully!</h1>
            <p>Thank you for verifying your email. A free coupon has been added to your account to try our service.</p>
            <p class="thank-you">We appreciate your support!</p>
            <p class="warning">An error occurred during the cleanup process, but your account is active.</p>
        </div>
    </body>
    </html>
    """
    return html

def get_error_html():
    """Return HTML for error during activation"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Activation Error</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #ff0000; }
            .icon { font-size: 64px; color: #ff0000; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">⚠️</div>
            <h1>An error occurred while processing your request.</h1>
        </div>
    </body>
    </html>
    """
    return html

def get_not_found_html():
    """Return HTML for 404 Not Found"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>404 Not Found</title>
        <style>
            body { font-family: Arial, sans-serif; background-color: #f2f2f2; text-align: center; padding: 50px; }
            .message-box { background-color: #fff; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: inline-block; }
            .message-box h1 { color: #ff0000; }
            .icon { font-size: 64px; color: #ff0000; }
        </style>
    </head>
    <body>
        <div class="message-box">
            <div class="icon">🔍</div>
            <h1>404 Not Found</h1>
        </div>
    </body>
    </html>
    """
    return html

def get_html_page():
    """Return HTML for the main page"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Account Activation</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
            }
            .form-group {
                margin-bottom: 15px;
            }
            label {
                display: block;
                margin-bottom: 5px;
            }
            input[type="text"], input[type="password"] {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }
            button {
                background-color: #4CAF50;
                color: white;
                padding: 10px 15px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background-color: #45a049;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Account Activation</h1>
            <p>Please enter your information to activate your account.</p>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="text" id="email" name="email" placeholder="Enter your email">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" placeholder="Enter your password">
            </div>
            
            <button id="activate">Activate Account</button>
            
            <div id="result" style="margin-top: 20px;"></div>
        </div>

        <script>
            document.getElementById('activate').addEventListener('click', function() {
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                if (!email || !password) {
                    document.getElementById('result').innerHTML = '<p style="color: red;">Please fill in all fields</p>';
                    return;
                }
                
                fetch('/api/ActivateAccounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        app_ID: '1'  // Default app ID
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error === 1) {
                        document.getElementById('result').innerHTML = 
                            '<p style="color: green;">Account activated successfully!</p>';
                    } else {
                        document.getElementById('result').innerHTML = 
                            `<p style="color: red;">Error: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p style="color: red;">Error: ${error.message}</p>`;
                });
            });
        </script>
    </body>
    </html>
    """
    return html

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')
    
    # Get the route from the request
    route = req.route_params.get('route', '')
    
    # Handle password reset submission endpoint
    if req.method == "POST" and route == "resetpassword":
        connection = None
        cursor = None
        try:
            req_body = req.get_json()
            email = req_body.get('email')
            password = req_body.get('password')
            confirmation_code = req_body.get('confirmation_code')
            
            if not all([email, password, confirmation_code]) or len(password) < 8:
                return func.HttpResponse(
                    json.dumps({'success': False, 'message': 'Invalid input data'}),
                    status_code=400,
                    mimetype="application/json"
                )
            
            # Get a connection from the pool
            if cnxpool:
                connection = cnxpool.get_connection()
            else:
                # Fall back to direct connection using shared config
                db_config = get_db_config()
                connection = mysql.connector.connect(**db_config)
            
            cursor = connection.cursor(dictionary=True)
            
            # Verify the confirmation code is valid and for password reset
            cursor.execute(
                "SELECT av.*, ia.email FROM account_verification av "
                "JOIN inactive_accounts ia ON av.Inactive_accounts_ID = ia.ID "
                "WHERE av.confirmation_code = %s AND av.Type = 2",
                (confirmation_code,)
            )
            verification = cursor.fetchone()
            
            if not verification or verification['end time'] < datetime.utcnow():
                return func.HttpResponse(
                    json.dumps({'success': False, 'message': 'Invalid or expired verification code'}),
                    status_code=400,
                    mimetype="application/json"
                )
            
            # Start transaction
            cursor.execute("START TRANSACTION")
            
            try:
                # First check if the email exists
                cursor.execute("SELECT 1 FROM activated_accounts WHERE email = %s LIMIT 1", (email,))
                email_exists = cursor.fetchone()
                
                if not email_exists:
                    connection.rollback()
                    return func.HttpResponse(
                        json.dumps({'success': False, 'message': 'Email not found'}),
                        status_code=400,
                        mimetype="application/json"
                    )
                
                # Update the user's password (whether it's the same or different)
                cursor.execute(
                    "UPDATE activated_accounts SET password = %s WHERE email = %s",
                    (password, email)
                )
                
                # Clean up the password reset verification data
                cursor.execute(
                    "DELETE av FROM account_verification av "
                    "JOIN inactive_accounts ia ON av.Inactive_accounts_ID = ia.ID "
                    "WHERE ia.email = %s AND av.Type = 2",
                    (email,)
                )
                cursor.execute(
                    "DELETE FROM inactive_accounts WHERE email = %s AND app_ID = 1",
                    (email,)
                )
                # Commit the transaction
                connection.commit()
                
                return func.HttpResponse(
                    json.dumps({'success': True, 'message': 'Password updated successfully'}),
                    status_code=200,
                    mimetype="application/json"
                )
            except Exception as e:
                # Rollback on any exception
                if connection:
                    connection.rollback()
                logging.error(f"Error in password update transaction: {e}")
                return func.HttpResponse(
                    json.dumps({'success': False, 'message': f'Transaction error: {str(e)}'}),
                    status_code=500,
                    mimetype="application/json"
                )
                
        except Exception as e:
            logging.error(f"Error in password reset endpoint: {e}")
            return func.HttpResponse(
                json.dumps({'success': False, 'message': f'Error: {str(e)}'}),
                status_code=500,
                mimetype="application/json"
            )
        finally:
            if cursor:
                cursor.close()
            if connection and connection.is_connected():
                connection.close()
    
    # Handle password reset success page
    if req.method == "GET" and route == "passwordreset/success":
        return func.HttpResponse(
            get_password_reset_success_html(),
            status_code=200,
            mimetype="text/html"
        )
    
    # Handle GET requests for activation
    if req.method == "GET":
        # Check if we have a token in the route
        token = None
        if route and not route.startswith('passwordreset'):
            # If route doesn't have a slash, treat it as a token
            if '/' not in route:
                token = route
        
        if token:
            # Process the confirmation code
            try:
                # Get a connection from the pool
                if cnxpool:
                    connection = cnxpool.get_connection()
                else:
                    # Fall back to direct connection using shared config
                    db_config = get_db_config()
                    connection = mysql.connector.connect(**db_config)
                
                cursor = connection.cursor(dictionary=True)
                
                # Activate account using confirmation code
                result = activate_account_from_verification(cursor, token)
                
                if not result[0]:  # If operation was not successful
                    if result[1] == "expired":
                        return func.HttpResponse(
                            get_expired_html(),
                            status_code=200,
                            mimetype="text/html"
                        )
                    elif result[1] == "invalid":
                        return func.HttpResponse(
                            get_invalid_html(),
                            status_code=200,
                            mimetype="text/html"
                        )
                    else:  # error
                        return func.HttpResponse(
                            get_error_html(),
                            status_code=200,
                            mimetype="text/html"
                        )
                else:
                    if result[1] == "password_reset":
                        # Show password reset form with email and confirmation code
                        email = result[2]
                        confirmation_code = result[3]
                        return func.HttpResponse(
                            get_password_reset_html(email, confirmation_code),
                            status_code=200,
                            mimetype="text/html"
                        )
                    elif result[1] == "success":
                        return func.HttpResponse(
                            get_success_html(),
                            status_code=200,
                            mimetype="text/html"
                        )
                    else:  # partial
                        return func.HttpResponse(
                            get_partial_success_html(),
                            status_code=200,
                            mimetype="text/html"
                        )
                        
            except mysql.connector.Error as e:
                logging.error(f"Database error: {e}")
                return func.HttpResponse(
                    get_error_html(),
                    status_code=200,
                    mimetype="text/html"
                )
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return func.HttpResponse(
                    get_error_html(),
                    status_code=200,
                    mimetype="text/html"
                )
            finally:
                if 'cursor' in locals() and cursor:
                    cursor.close()
                if 'connection' in locals() and connection.is_connected():
                    connection.close()
        elif route == "":
            # Return the main HTML page for direct access to the function URL
            return func.HttpResponse(
                get_html_page(),
                status_code=200,
                mimetype="text/html"
            )
        else:
            # Return 404 Not Found for invalid routes
            return func.HttpResponse(
                get_not_found_html(),
                status_code=404,
                mimetype="text/html"
            )
    
    # Handle POST requests (original functionality)
    try:
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse(json.dumps({'error': 6001}), status_code=200, mimetype="application/json")
    
    # Extract parameters
    email = req_body.get('email')
    password = req_body.get('password')
    app_ID = req_body.get('app_ID')
    
    # Validate parameters
    if not all([email, password, app_ID]):
        return func.HttpResponse(json.dumps({'error': 5001}), status_code=200, mimetype="application/json")
    
    if not validate_email(email):
        return func.HttpResponse(json.dumps({'error': 1001}), status_code=200, mimetype="application/json")
    
    if not validate_password(password):
        return func.HttpResponse(json.dumps({'error': 1002}), status_code=200, mimetype="application/json")
    
    try:
        # Get a connection from the pool
        if cnxpool:
            connection = cnxpool.get_connection()
        else:
            # Fall back to direct connection using shared config
            db_config = get_db_config()
            connection = mysql.connector.connect(**db_config)
        
        cursor = connection.cursor(dictionary=True)
        
        # Check if email exists
        if check_existing_email(cursor, email):
            return func.HttpResponse(json.dumps({'error': 1003}), status_code=200, mimetype="application/json")
        
        # Directly activate the account (no verification link needed)
        success, account_id = activate_account(cursor, email, password, app_ID)
        
        if success:
            return func.HttpResponse(json.dumps({'error': 1, 'message': 'Account activated successfully', 'account_id': account_id}), 
                                    status_code=200, mimetype="application/json")
        else:
            return func.HttpResponse(json.dumps({'error': 0}), status_code=200, mimetype="application/json")
            
    except mysql.connector.Error as e:
        logging.error(f"Database error: {e}")
        return func.HttpResponse(json.dumps({'error': 0}), status_code=200, mimetype="application/json")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return func.HttpResponse(json.dumps({'error': 11001}), status_code=200, mimetype="application/json")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            