import os
import logging

# Database configuration with environment variable fallback
def get_db_config():
    """
    Returns database configuration with priority given to environment variables.
    Used for centralizing database credentials across all functions.
    """
    return {
        'host': os.environ.get('DB_HOST', 'xxxx.mysql.database.azure.com'),
        'user': os.environ.get('DB_USER', 'one1'),
        'password': os.environ.get('DB_PASSWORD', '773105987Ass'),
        'database': os.environ.get('DB_NAME', 'one'),
        'connection_timeout': int(os.environ.get('DB_TIMEOUT', 30)),
        'pool_size': int(os.environ.get('DB_POOL_SIZE', 5))
    }

# Email configuration with environment variable fallback
def get_email_config():
    """
    Returns email configuration with priority given to environment variables.
    Used for centralizing email credentials across all functions.
    """
    return {
        'smtp_server': os.environ.get('SMTP_SERVER', 'smtp.gmail.com'),
        'smtp_port': int(os.environ.get('SMTP_PORT', 587)),
        'smtp_user': os.environ.get('SMTP_USER', '<EMAIL>'),
        'smtp_password': os.environ.get('SMTP_PASSWORD', 'ylbqbiopskcivzpq')
    }

# Function to create a connection pool
def create_connection_pool(pool_name):
    """
    Creates and returns a database connection pool with the specified name.
    """
    try:
        import mysql.connector
        from mysql.connector import pooling
        
        db_config = get_db_config()
        cnxpool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name=pool_name,
            **db_config
        )
        logging.info(f"Database connection pool '{pool_name}' created successfully")
        return cnxpool
    except Exception as e:
        logging.error(f"Failed to create connection pool '{pool_name}': {str(e)}")
        return None 