import json
import logging
import os
import uuid
import concurrent.futures
from functools import lru_cache
import azure.functions as func
from azure.storage.blob import BlobServiceClient, ContentSettings, BlobClient
from azure.core.exceptions import ResourceExistsError, ServiceRequestError, ResourceNotFoundError
import mysql.connector
from mysql.connector import Error, pooling
import requests
from requests.adapters import HTTPAdapter
from typing import Dict, Any
import sys

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, create_connection_pool

# Create a custom session with strict timeouts
def create_strict_session():
    session = requests.Session()
    adapter = HTTPAdapter(max_retries=0)  # Disable retries
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

# Azure Storage connection string - direct connection string value
CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=ragxxxxblobstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

# Container name
CONTAINER_NAME = "inputfiles"

# Create a connection pool for better performance
cnxpool = create_connection_pool("fileupload_pool")

# Error codes
ERROR_CODES = {
    1000: "Missing required parameters",
    1001: "Invalid app_id format",
    2000: "Database connection failed",
    2001: "Username not found",
    "00000": "Invalid session for the user and application",
    3000: "Container does not exist",
    3001: "No files were uploaded",
    3002: "Azurite connection failed",
    5000: "General server error"
}

# Content type mapping for better performance - avoid conditional checks for each file
CONTENT_TYPE_MAP = {
    '.pdf': 'application/pdf',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.txt': 'text/plain',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.csv': 'text/csv',
    '.json': 'application/json',
    '.xml': 'application/xml',
}

def get_content_type(filename):
    """Determine content type based on file extension"""
    ext = os.path.splitext(filename.lower())[1]
    return CONTENT_TYPE_MAP.get(ext, 'application/octet-stream')

@lru_cache(maxsize=128)
def validate_session(session_number, username, app_id):
    """Validate user session with caching for better performance"""
    try:
        # Convert username to email format by appending @gmail.com
        email = f"{username}@gmail.com"
        
        # Get connection from pool for better performance
        if cnxpool:
            conn = cnxpool.get_connection()
        else:
            # Fall back to direct connection using shared config
            db_config = get_db_config()
            conn = mysql.connector.connect(**db_config)
            
        if conn.is_connected():
            cursor = conn.cursor(dictionary=True)
            
            # Get the activated_accounts_ID for the email
            cursor.execute("SELECT ID FROM activated_accounts WHERE email = %s", (email,))
            account_result = cursor.fetchone()
            
            if not account_result:
                return False, 2001, f"Username not found: {email}"
            
            account_id = account_result['ID']
            
            # Validate session number
            query = """
            SELECT 1 FROM session_numners 
            WHERE session_number = %s 
            AND activated_accounts_ID = %s 
            AND app_ID = %s
            LIMIT 1
            """
            cursor.execute(query, (session_number, account_id, app_id))
            
            session = cursor.fetchone()
            
            if session:
                return True, None, None
            else:
                return False, "00000", "Invalid session for the user and application"
            
    except Error as e:
        return False, 2000, f"Database error: {str(e)}"
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn and conn.is_connected():
            conn.close()

@lru_cache(maxsize=16)
def ensure_container_exists(blob_service_client, container_name):
    """Check if container exists without creating it"""
    try:
        container_client = blob_service_client.get_container_client(container_name)
        # Check if container exists
        try:
            container_client.get_container_properties()
            logging.info(f"Container '{container_name}' already exists.")
            return container_client, None
        except ResourceNotFoundError:
            # Container doesn't exist, return error instead of creating it
            error_msg = f"Container '{container_name}' does not exist"
            logging.error(error_msg)
            return None, error_msg
    except ServiceRequestError as e:
        error_msg = f"Failed to connect to Azurite: {str(e)}"
        logging.error(error_msg)
        return None, error_msg
    except Exception as e:
        error_msg = f"Error accessing container: {str(e)}"
        logging.error(error_msg)
        return None, error_msg

def upload_file_async(file_data, folder_id, connection_string, container_name):
    """Asynchronously upload a file to blob storage"""
    filename, content = file_data
    blob_name = f"{folder_id}/{filename}"
    content_type = get_content_type(filename)
    
    # We'll use the sync version instead as it gives more control over the timeout
    try:
        result = upload_file_sync(filename, content, folder_id, connection_string, container_name)
        return result
    except Exception as e:
        logging.error(f"Error uploading file {filename}: {str(e)}")
        return {
            "filename": filename,
            "blob_name": blob_name,
            "url": None,
            "success": False,
            "error": str(e)
        }

def upload_files_parallel(files, folder_id, connection_string, container_name, max_workers=10):
    """Upload files in parallel using a thread pool"""
    uploaded_files = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all upload tasks
        future_to_file = {
            executor.submit(
                upload_file_sync, 
                filename, 
                content, 
                folder_id, 
                connection_string, 
                container_name
            ): filename 
            for filename, content in files.items()
        }
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_file):
            filename = future_to_file[future]
            try:
                result = future.result()
                if result.get("success", False):
                    uploaded_files.append({
                        "filename": result["filename"],
                        "blob_name": result["blob_name"],
                        "url": result["url"]
                    })
                    logging.info(f"Uploaded file: {filename} to {result['blob_name']}")
                else:
                    logging.error(f"Failed to upload file {filename}: {result.get('error')}")
            except Exception as e:
                logging.error(f"Exception while uploading file {filename}: {str(e)}")
    
    return uploaded_files

def upload_file_sync(filename, content, folder_id, connection_string, container_name):
    """Synchronous version of the file upload function for use with thread pool"""
    blob_name = f"{folder_id}/{filename}"
    content_type = get_content_type(filename)
    
    try:
        # Create a session with strict timeouts
        session = create_strict_session()
        
        # Create blob client with custom session
        blob_client = BlobClient.from_connection_string(
            connection_string,
            container_name=container_name,
            blob_name=blob_name,
            connection_timeout=3,  # Set timeout to 3 seconds
            _request_options={'timeout': 3},
            session=session
        )
        
        # Upload the blob with a strict timeout
        blob_client.upload_blob(
            content,
            overwrite=True,
            content_settings=ContentSettings(content_type=content_type),
            timeout=3
        )
        
        # Get public URL for the blob
        blob_url = blob_client.url
        
        return {
            "filename": filename,
            "blob_name": blob_name,
            "url": blob_url,
            "success": True,
            "error": None
        }
    except Exception as e:
        return {
            "filename": filename,
            "blob_name": blob_name,
            "url": None,
            "success": False,
            "error": str(e)
        }

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('File Upload function processing a request.')
    
    try:
        # Get parameters from form data or query string
        session_number = req.form.get('session_number') or req.params.get('session_number')
        username = req.form.get('username') or req.params.get('username')
        app_id = req.form.get('app_id') or req.params.get('app_id')
        
        # If parameters not found in form or query, try to get from JSON body
        if not all([session_number, username, app_id]):
            try:
                req_body = req.get_json()
                session_number = session_number or req_body.get('session_number')
                username = username or req_body.get('username')
                app_id = app_id or req_body.get('app_id')
            except ValueError:
                pass
        
        # Validate required parameters
        if not all([session_number, username, app_id]):
            return func.HttpResponse(
                json.dumps({"error": "Missing required parameters: session_number, username, app_id", "error_code": 1000}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Convert app_id to int if it's a string
        try:
            app_id = int(app_id)
        except (ValueError, TypeError):
            return func.HttpResponse(
                json.dumps({"error": "app_id must be a valid integer", "error_code": 1001}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Validate session - now using cached version
        valid_session, error_code, error_message = validate_session(session_number, username, app_id)
        if not valid_session:
            return func.HttpResponse(
                json.dumps({"error": f"Session validation failed: {error_message}", "error_code": error_code}),
                status_code=401,
                mimetype="application/json"
            )
        
        # Initialize BlobServiceClient
        try:
            # Create a session with strict timeouts
            session = create_strict_session()
            
            blob_service_client = BlobServiceClient.from_connection_string(
                CONNECTION_STRING,
                connection_timeout=3,  # Set timeout to 3 seconds
                _request_options={'timeout': 3},
                session=session
            )
            logging.info("Successfully connected to Azure Blob Storage.")
        except Exception as e:
            error_msg = f"Failed to connect to Azure Blob Storage: {str(e)}"
            logging.error(error_msg)
            return func.HttpResponse(
                json.dumps({"error": error_msg, "error_code": 3002}),
                status_code=500,
                mimetype="application/json"
            )
        
        # Ensure container exists - with timeout and custom session
        try:
            container_client = blob_service_client.get_container_client(CONTAINER_NAME)
            # Check if container exists with timeout
            container_client.get_container_properties(timeout=3)
            logging.info(f"Container '{CONTAINER_NAME}' already exists.")
        except ResourceNotFoundError:
            # Container doesn't exist, return error instead of creating it
            error_msg = f"Container '{CONTAINER_NAME}' does not exist"
            logging.error(error_msg)
            return func.HttpResponse(
                json.dumps({"error": error_msg, "error_code": 3000}),
                status_code=500,
                mimetype="application/json"
            )
        except ServiceRequestError as e:
            error_msg = f"Failed to connect to Azurite: {str(e)}"
            logging.error(error_msg)
            return func.HttpResponse(
                json.dumps({"error": error_msg, "error_code": 3002}),
                status_code=500,
                mimetype="application/json"
            )
        except Exception as e:
            error_msg = f"Error accessing container: {str(e)}"
            logging.error(error_msg)
            return func.HttpResponse(
                json.dumps({"error": error_msg, "error_code": 3000}),
                status_code=500,
                mimetype="application/json"
            )
        
        # Generate a unique folder ID for this upload batch
        folder_id = uuid.uuid4().hex
        
        # Get all files from the request
        files = {}
        for input_file in req.files.values():
            # Read file content
            file_content = input_file.read()
            # Store file name and content
            files[input_file.filename] = file_content
        
        if not files:
            return func.HttpResponse(
                json.dumps({"error": "No files were uploaded", "error_code": 3001}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Use parallel upload for better performance
        uploaded_files = upload_files_parallel(
            files,
            folder_id,
            CONNECTION_STRING,
            CONTAINER_NAME,
            max_workers=min(10, len(files))  # Adjust workers based on file count
        )
        
        # If all uploads failed, return an error
        if not uploaded_files:
            return func.HttpResponse(
                json.dumps({
                    "error": "All file uploads failed",
                    "error_code": 5000
                }),
                status_code=500,
                mimetype="application/json"
            )
        
        # Return success response with folder ID
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "folder_id": folder_id,
                "file_count": len(uploaded_files),
                "files": uploaded_files
            }),
            status_code=200,
            mimetype="application/json"
        )
    
    except Exception as e:
        error_msg = f"Error processing request: {str(e)}"
        logging.error(error_msg)
        return func.HttpResponse(
            json.dumps({
                "error": error_msg,
                "error_code": 5000
            }),
            status_code=500,
            mimetype="application/json"
        ) 
        