import json
import logging
import azure.functions as func
import mysql.connector
from mysql.connector import pooling
import uuid
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
import pytz
import re
import asyncio
import aiosmtplib
from functools import lru_cache
import concurrent.futures
import os
import sys

# Add the project root to the path to access shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared_config import get_db_config, get_email_config, create_connection_pool

# Create a connection pool for better performance
cnxpool = create_connection_pool("signup_pool")

# Get email configuration
email_config = get_email_config()
smtp_server = email_config['smtp_server']
smtp_port = email_config['smtp_port']
smtp_user = email_config['smtp_user']
smtp_password = email_config['smtp_password']

# Cache for email validation pattern
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@gmail\.com$')

# Precompile regular expressions
@lru_cache(maxsize=1)
def get_email_pattern():
    return EMAIL_PATTERN

def generate_confirmation_code():
    return uuid.uuid4().hex

async def send_verification_email_async(recipient_email, verification_link, is_password_reset=False):
    """Asynchronous email sending for better performance"""
    try:
        # Ensure the link uses HTTPS
        if verification_link.startswith('http:'):
            verification_link = 'https:' + verification_link[5:]
        elif not verification_link.startswith('https:'):
            verification_link = 'https://' + verification_link.lstrip('/')
            
        message = MIMEMultipart()
        message['From'] = smtp_user
        message['To'] = recipient_email
        
        if is_password_reset:
            message['Subject'] = 'AI BOOSTER - Reset Your Password'
            button_text = "Reset Password"
            email_heading = "Password Reset"
            email_text = "You have requested to reset your password for <strong>AI BOOSTER</strong>. To complete this process, please click the button below:"
        else:
            message['Subject'] = 'AI BOOSTER - Activate Your Account'
            button_text = "Activate Account"
            email_heading = "Account Activation"
            email_text = "Thank you for installing <strong>AI BOOSTER</strong>! To complete your registration and start using all the powerful features, please activate your account by clicking the button below:"
        
        # Create HTML version only with button
        html = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333333; background-color: #f9f9f9; margin: 0; padding: 0;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-top: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #4285F4; margin-bottom: 10px; font-size: 28px;">AI BOOSTER</h1>
                    <p style="font-size: 18px; color: #5f6368; margin-bottom: 0;">{email_heading}</p>
                </div>
                <div style="border-top: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0; padding: 30px 0; margin-bottom: 30px;">
                    <p style="font-size: 16px; margin-bottom: 25px;">{email_text}</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{verification_link}" style="background-color: #4285F4; color: white; padding: 14px 28px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; font-size: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.15);">{button_text}</a>
                    </div>
                </div>
                <p style="font-size: 14px; color: #5f6368; text-align: center;">If you did not request this, please disregard this email.</p>
                <div style="text-align: center; margin-top: 30px; color: #9e9e9e; font-size: 12px;">
                    <p>&copy; 2023 AI BOOSTER. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version without raw link
        if is_password_reset:
            text = "You have requested to reset your password for AI BOOSTER. To complete this process, please check your email and click on the Reset Password button we've sent you."
        else:
            text = "Thank you for installing AI BOOSTER! To complete your registration, please check your email and click on the Activate Account button we've sent you."
        
        # Attach parts
        part1 = MIMEText(text, 'plain')
        part2 = MIMEText(html, 'html')
        message.attach(part1)
        message.attach(part2)
        
        # Connect to server and send
        async with aiosmtplib.SMTP(smtp_server, smtp_port) as server:
            await server.starttls()
            await server.login(smtp_user, smtp_password)
            await server.send_message(message)
        
        logging.info("Verification email sent successfully.")
        return True
    except Exception as e:
        logging.error(f"Failed to send verification email: {e}")
        return False

def send_verification_email(recipient_email, verification_link, is_password_reset=False):
    """Synchronous fallback for email sending"""
    try:
        # Ensure the link uses HTTPS
        if verification_link.startswith('http:'):
            verification_link = 'https:' + verification_link[5:]
        elif not verification_link.startswith('https:'):
            verification_link = 'https://' + verification_link.lstrip('/')
            
        message = MIMEMultipart()
        message['From'] = smtp_user
        message['To'] = recipient_email
        
        if is_password_reset:
            message['Subject'] = 'AI BOOSTER - Reset Your Password'
            button_text = "Reset Password"
            email_heading = "Password Reset"
            email_text = "You have requested to reset your password for <strong>AI BOOSTER</strong>. To complete this process, please click the button below:"
        else:
            message['Subject'] = 'AI BOOSTER - Activate Your Account'
            button_text = "Activate Account"
            email_heading = "Account Activation"
            email_text = "Thank you for installing <strong>AI BOOSTER</strong>! To complete your registration and start using all the powerful features, please activate your account by clicking the button below:"
        
        # Create HTML version only with button
        html = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333333; background-color: #f9f9f9; margin: 0; padding: 0;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-top: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #4285F4; margin-bottom: 10px; font-size: 28px;">AI BOOSTER</h1>
                    <p style="font-size: 18px; color: #5f6368; margin-bottom: 0;">{email_heading}</p>
                </div>
                <div style="border-top: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0; padding: 30px 0; margin-bottom: 30px;">
                    <p style="font-size: 16px; margin-bottom: 25px;">{email_text}</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{verification_link}" style="background-color: #4285F4; color: white; padding: 14px 28px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; font-size: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.15);">{button_text}</a>
                    </div>
                </div>
                <p style="font-size: 14px; color: #5f6368; text-align: center;">If you did not request this, please disregard this email.</p>
                <div style="text-align: center; margin-top: 30px; color: #9e9e9e; font-size: 12px;">
                    <p>&copy; 2023 AI BOOSTER. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version without raw link
        if is_password_reset:
            text = "You have requested to reset your password for AI BOOSTER. To complete this process, please check your email and click on the Reset Password button we've sent you."
        else:
            text = "Thank you for installing AI BOOSTER! To complete your registration, please check your email and click on the Activate Account button we've sent you."
        
        # Attach parts
        part1 = MIMEText(text, 'plain')
        part2 = MIMEText(html, 'html')
        message.attach(part1)
        message.attach(part2)
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_user, smtp_password)
            server.send_message(message)
        logging.info("Verification email sent successfully.")
        return True
    except Exception as e:
        logging.error(f"Failed to send verification email: {e}")
        return False

@lru_cache(maxsize=100)
def validate_email(email):
    """Cached email validation for better performance"""
    pattern = get_email_pattern()
    return bool(pattern.match(email))

@lru_cache(maxsize=100)
def validate_password(password):
    """Simple password validation with caching"""
    return len(password) >= 8

def check_existing_email(cursor, email):
    """Check if email already exists in database"""
    cursor.execute("SELECT 1 FROM activated_accounts WHERE email = %s LIMIT 1", (email,))
    return cursor.fetchone() is not None

def check_email_exists(cursor, email):
    """Check if email exists in activated_accounts"""
    cursor.execute("SELECT 1 FROM activated_accounts WHERE email = %s LIMIT 1", (email,))
    result = cursor.fetchone()
    # Handle both dictionary and non-dictionary cursors
    if isinstance(result, dict):
        return result is not None
    else:
        return result is not None

def insert_inactive_account(cursor, email, password, app_ID):
    """Insert new inactive account"""
    cursor.execute(
        "INSERT INTO inactive_accounts (email, password, app_ID) VALUES (%s, %s, %s)", 
        (email, password, app_ID)
    )
    return cursor.lastrowid

def insert_verification(cursor, inactive_account_id, confirmation_code, production_time, end_time, type_value=1):
    """Insert verification record"""
    cursor.execute(
        "INSERT INTO account_verification (Inactive_accounts_ID, confirmation_code, production_time, `end time`, `Type`) VALUES (%s, %s, %s, %s, %s)", 
        (inactive_account_id, confirmation_code, production_time, end_time, type_value)
    )

# Cache for confirmation portal URL
@lru_cache(maxsize=1)
def get_confirmation_portal_url():
    """Get the confirmation portal URL from the database"""
    try:
        # Get a connection from the pool or create a new one using shared config
        if cnxpool:
            connection = cnxpool.get_connection()
        else:
            # Fall back to direct connection using shared config
            db_config = get_db_config()
            connection = mysql.connector.connect(**db_config)
        
        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)
            cursor.execute("SELECT id_confirmation_portal_url FROM confirmation_portal LIMIT 1")
            result = cursor.fetchone()
            
            if result and result['id_confirmation_portal_url']:
                return result['id_confirmation_portal_url']
            else:
                logging.warning("No confirmation portal URL found in database, using default")
                return "http://localhost:80/"
    except Exception as e:
        logging.error(f"Failed to fetch confirmation portal URL: {str(e)}")
        return "http://localhost:80/"  # Fallback to default URL
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()

async def process_signup_async(email, password, app_ID):
    """Process signup asynchronously"""
    # Get a connection from the pool or create a new one using shared config
    if cnxpool:
        connection = cnxpool.get_connection()
    else:
        db_config = get_db_config()
        connection = mysql.connector.connect(**db_config)
    
    try:
        # Use dictionary cursor for consistency
        cursor = connection.cursor(dictionary=True)
        
        # Check if email exists
        if check_existing_email(cursor, email):
            return {'error': 1003}
        
        # Insert inactive account
        inactive_account_id = insert_inactive_account(cursor, email, password, app_ID)
        connection.commit()
        
        # Generate confirmation code and times
        confirmation_code = generate_confirmation_code()
        production_time = datetime.utcnow().replace(tzinfo=pytz.utc)
        end_time = production_time + timedelta(hours=1)
        
        # Insert verification record
        insert_verification(cursor, inactive_account_id, confirmation_code, production_time, end_time, 1)
        connection.commit()
        
        # Get confirmation portal URL from database
        portal_url = get_confirmation_portal_url()
        
        # Create verification link
        verification_link = f"{portal_url}{confirmation_code}"
        
        # Send email asynchronously
        email_success = await send_verification_email_async(email, verification_link)
        if not email_success:
            return {'error': 2001}
        
        return {'error': 1}
    except mysql.connector.Error as e:
        logging.error(f"Database error: {e}")
        return {'error': 0}
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return {'error': 11001}
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()

async def process_password_reset_async(email):
    """Process password reset request asynchronously"""
    # Get a connection from the pool or create a new one using shared config
    if cnxpool:
        connection = cnxpool.get_connection()
    else:
        db_config = get_db_config()
        connection = mysql.connector.connect(**db_config)
    
    try:
        # Use dictionary cursor for consistency
        cursor = connection.cursor(dictionary=True)
        
        # Check if email exists in activated accounts
        if not check_email_exists(cursor, email):
            return {'error': 1004}  # Email not found
        
        # Insert inactive account with placeholder password
        placeholder_password = "-"  # Placeholder for password reset
        app_ID = 1   # Use default app ID instead of string "password_reset"
        
        inactive_account_id = insert_inactive_account(cursor, email, placeholder_password, app_ID)
        connection.commit()
        
        # Generate confirmation code and times
        confirmation_code = generate_confirmation_code()
        production_time = datetime.utcnow().replace(tzinfo=pytz.utc)
        end_time = production_time + timedelta(hours=1)
        
        # Insert verification record with Type=2 for password reset
        insert_verification(cursor, inactive_account_id, confirmation_code, production_time, end_time, 2)
        connection.commit()
        
        # Get confirmation portal URL from database
        portal_url = get_confirmation_portal_url()
        
        # Create verification link
        verification_link = f"{portal_url}{confirmation_code}"
        
        # Send email asynchronously
        email_success = await send_verification_email_async(email, verification_link, is_password_reset=True)
        if not email_success:
            return {'error': 2001}
        
        return {'error': 1}
    except mysql.connector.Error as e:
        logging.error(f"Database error: {e}")
        return {'error': 0}
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return {'error': 11001}
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')
    
    # Parse request body
    try:
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse(json.dumps({'error': 6001}), status_code=200, mimetype="application/json")
    
    # Extract parameters
    email = req_body.get('email')
    password = req_body.get('password')
    app_ID = req_body.get('app_ID')
    
    # Validate email parameter (required for both account creation and password reset)
    if not email:
        return func.HttpResponse(json.dumps({'error': 5001}), status_code=200, mimetype="application/json")
    
    if not validate_email(email):
        return func.HttpResponse(json.dumps({'error': 1001}), status_code=200, mimetype="application/json")
    
    # Check if this is a password reset request (only email provided)
    is_password_reset = password is None or password == ""
    
    if is_password_reset:
        # Password reset flow
        try:
            # Try to use asyncio for better performance
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(process_password_reset_async(email))
            return func.HttpResponse(json.dumps(result), status_code=200, mimetype="application/json")
        except (RuntimeError, ImportError):
            # Fallback to synchronous processing
            try:
                # Get a connection from the pool or create a new one using shared config
                if cnxpool:
                    connection = cnxpool.get_connection()
                else:
                    db_config = get_db_config()
                    connection = mysql.connector.connect(**db_config)
                
                # Use dictionary cursor for consistency
                cursor = connection.cursor(dictionary=True)
                
                # Check if email exists in activated accounts
                if not check_email_exists(cursor, email):
                    return func.HttpResponse(json.dumps({'error': 1004}), status_code=200, mimetype="application/json")
                
                # Insert inactive account with placeholder password
                placeholder_password = "-"  # Placeholder for password reset
                app_ID = 1   # Use default app ID instead of string "password_reset"
                
                cursor.execute("INSERT INTO inactive_accounts (email, password, app_ID) VALUES (%s, %s, %s)", 
                            (email, placeholder_password, app_ID))
                connection.commit()
                inactive_account_id = cursor.lastrowid
                
                # Generate confirmation code and times
                confirmation_code = generate_confirmation_code()
                production_time = datetime.utcnow().replace(tzinfo=pytz.utc)
                end_time = production_time + timedelta(hours=1)
                
                # Insert verification record with Type=2 for password reset
                cursor.execute(
                    "INSERT INTO account_verification (Inactive_accounts_ID, confirmation_code, production_time, `end time`, `Type`) VALUES (%s, %s, %s, %s, %s)", 
                    (inactive_account_id, confirmation_code, production_time, end_time, 2)
                )
                connection.commit()
                
                # Get confirmation portal URL from database
                portal_url = get_confirmation_portal_url()
                
                # Create verification link
                verification_link = f"{portal_url}{confirmation_code}"
                
                # Send email synchronously
                email_sent = send_verification_email(email, verification_link, is_password_reset=True)
                if not email_sent:
                    return func.HttpResponse(json.dumps({'error': 2001}), status_code=200, mimetype="application/json")
                
                return func.HttpResponse(json.dumps({'error': 1}), status_code=200, mimetype="application/json")
            except mysql.connector.Error as e:
                logging.error(f"Database error: {e}")
                return func.HttpResponse(json.dumps({'error': 0}), status_code=200, mimetype="application/json")
            except smtplib.SMTPException as e:
                return func.HttpResponse(json.dumps({'error': 2001}), status_code=200, mimetype="application/json")
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return func.HttpResponse(json.dumps({'error': 11001}), status_code=200, mimetype="application/json")
            finally:
                if 'cursor' in locals() and cursor:
                    cursor.close()
                if 'connection' in locals() and connection.is_connected():
                    connection.close()
    else:
        # Account creation flow - validate additional parameters
        if not password or not app_ID:
            return func.HttpResponse(json.dumps({'error': 5001}), status_code=200, mimetype="application/json")
        
        if not validate_password(password):
            return func.HttpResponse(json.dumps({'error': 1002}), status_code=200, mimetype="application/json")
        
        # Check if we can use asyncio
        try:
            # Try to use asyncio for better performance
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(process_signup_async(email, password, app_ID))
            return func.HttpResponse(json.dumps(result), status_code=200, mimetype="application/json")
        except (RuntimeError, ImportError):
            # Fallback to synchronous processing
            try:
                # Get a connection from the pool or create a new one using shared config
                if cnxpool:
                    connection = cnxpool.get_connection()
                else:
                    db_config = get_db_config()
                    connection = mysql.connector.connect(**db_config)
                
                # Use dictionary cursor for consistency
                cursor = connection.cursor(dictionary=True)
                
                # Check if email exists
                if check_existing_email(cursor, email):
                    return func.HttpResponse(json.dumps({'error': 1003}), status_code=200, mimetype="application/json")
                
                # Insert inactive account
                cursor.execute("INSERT INTO inactive_accounts (email, password, app_ID) VALUES (%s, %s, %s)", 
                            (email, password, app_ID))
                connection.commit()
                inactive_account_id = cursor.lastrowid
                
                # Generate confirmation code and times
                confirmation_code = generate_confirmation_code()
                production_time = datetime.utcnow().replace(tzinfo=pytz.utc)
                end_time = production_time + timedelta(hours=1)
                
                # Insert verification record with Type=1 for account creation
                cursor.execute(
                    "INSERT INTO account_verification (Inactive_accounts_ID, confirmation_code, production_time, `end time`, `Type`) VALUES (%s, %s, %s, %s, %s)", 
                    (inactive_account_id, confirmation_code, production_time, end_time, 1)
                )
                connection.commit()
                
                # Get confirmation portal URL from database
                portal_url = get_confirmation_portal_url()
                
                # Create verification link
                verification_link = f"{portal_url}{confirmation_code}"
                
                # Send email synchronously
                email_sent = send_verification_email(email, verification_link)
                if not email_sent:
                    return func.HttpResponse(json.dumps({'error': 2001}), status_code=200, mimetype="application/json")
                
                return func.HttpResponse(json.dumps({'error': 1}), status_code=200, mimetype="application/json")
            except mysql.connector.Error as e:
                logging.error(f"Database error: {e}")
                return func.HttpResponse(json.dumps({'error': 0}), status_code=200, mimetype="application/json")
            except smtplib.SMTPException as e:
                return func.HttpResponse(json.dumps({'error': 2001}), status_code=200, mimetype="application/json")
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return func.HttpResponse(json.dumps({'error': 11001}), status_code=200, mimetype="application/json")
            finally:
                if 'cursor' in locals() and cursor:
                    cursor.close()
                if 'connection' in locals() and connection.is_connected():
                    connection.close()